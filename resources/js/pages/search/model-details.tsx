import { Head, Link, router, usePage } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
    ArrowLeft,
    Smartphone,
    Calendar,
    Hash,
    Building,
    Search,
    Heart
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useState } from 'react';
import { toast } from 'sonner';
import type { SharedData } from '@/types';

interface ModelSpecifications {
    [key: string]: string | number | boolean | null;
}

interface MobileModel {
    id: number;
    name: string;
    slug?: string;
    model_number: string | null;
    release_year: number | null;
    specifications: ModelSpecifications | null;
    images: string[] | null;
    is_active: boolean;
    brand: {
        id: number;
        name: string;
        slug?: string;
        logo_url: string | null;
    };
    parts: Array<{
        id: number;
        name: string;
        slug?: string;
        part_number: string | null;
        manufacturer: string | null;
        description: string | null;
        category: {
            id: number;
            name: string;
        };
    }>;
}

interface Props {
    model: MobileModel;
}

export default function ModelDetails({ model }: Props) {
    const { auth } = usePage<SharedData>().props;
    const [isAddingToFavorites, setIsAddingToFavorites] = useState(false);
    const [isSaved, setIsSaved] = useState(false);

    const handleAddToFavorites = () => {
        // Check if user is authenticated
        if (!auth.user) {
            toast.error('Please log in to add items to favorites', {
                description: 'You need to be logged in to save favorites.',
            });
            return;
        }

        // Check if already favoriting
        if (isAddingToFavorites) {
            return;
        }

        // Check if already favorited
        if (isSaved) {
            toast.info('Already in favorites', {
                description: 'This model is already in your favorites.',
            });
            return;
        }

        setIsAddingToFavorites(true);

        router.post(route('dashboard.add-favorite'), {
            type: 'model',
            id: model.id,
        }, {
            onSuccess: () => {
                setIsSaved(true);
                setIsAddingToFavorites(false);
                toast.success('Added to favorites!', {
                    description: 'The model has been saved to your favorites.',
                });
            },
            onError: (errors) => {
                setIsAddingToFavorites(false);

                // Handle specific error cases
                if (errors && typeof errors === 'object' && 'message' in errors) {
                    toast.error('Failed to add to favorites', {
                        description: errors.message as string,
                    });
                } else {
                    toast.error('Failed to add to favorites', {
                        description: 'Please try again or contact support.',
                    });
                }
            }
        });
    };

    return (
        <AppLayout>
            <Head title={`${model.brand.name} ${model.name}`} />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    {/* Breadcrumb */}
                    <div className="flex items-center gap-2 mb-6 text-sm text-gray-600">
                        <Link href={route('search.index')} className="hover:text-gray-900">
                            Search
                        </Link>
                        <span>/</span>
                        <Link href={route('brands.show', model.brand.slug || model.brand.id)} className="hover:text-gray-900">
                            {model.brand.name}
                        </Link>
                        <span>/</span>
                        <span className="text-gray-900">{model.name}</span>
                    </div>

                    <div className="grid lg:grid-cols-3 gap-8">
                        {/* Main Content */}
                        <div className="lg:col-span-2 space-y-6">
                            {/* Header */}
                            <Card>
                                <CardContent className="p-6">
                                    <div className="flex items-start gap-4 mb-4">
                                        <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                                            {model.brand.logo_url ? (
                                                <img 
                                                    src={model.brand.logo_url} 
                                                    alt={`${model.brand.name} logo`}
                                                    className="w-full h-full object-contain"
                                                />
                                            ) : (
                                                <Smartphone className="w-8 h-8 text-gray-400" />
                                            )}
                                        </div>
                                        <div className="flex-1">
                                            <h1 className="text-3xl font-bold text-gray-900 mb-2">
                                                {model.brand.name} {model.name}
                                            </h1>
                                            <div className="flex items-center gap-4 text-sm text-gray-600 mb-4">
                                                {model.model_number && (
                                                    <span className="flex items-center gap-1">
                                                        <Hash className="w-4 h-4" />
                                                        {model.model_number}
                                                    </span>
                                                )}
                                                {model.release_year && (
                                                    <span className="flex items-center gap-1">
                                                        <Calendar className="w-4 h-4" />
                                                        {model.release_year}
                                                    </span>
                                                )}
                                                <Badge variant={model.is_active ? "default" : "secondary"}>
                                                    {model.is_active ? "Active" : "Inactive"}
                                                </Badge>
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Specifications */}
                            {model.specifications && Object.keys(model.specifications).length > 0 && (
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Specifications</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            {Object.entries(model.specifications).map(([key, value]) => (
                                                <div key={key} className="flex justify-between py-2 border-b border-gray-100 last:border-0">
                                                    <span className="font-medium text-gray-600 capitalize">
                                                        {key.replace(/_/g, ' ')}:
                                                    </span>
                                                    <span className="text-gray-900">{value}</span>
                                                </div>
                                            ))}
                                        </div>
                                    </CardContent>
                                </Card>
                            )}




                        </div>

                        {/* Sidebar */}
                        <div className="space-y-6">
                            {/* Model Info */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Smartphone className="w-5 h-5" />
                                        Model Information
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        <div>
                                            <span className="text-sm font-medium text-gray-600">Brand:</span>
                                            <Link 
                                                href={route('brands.show', model.brand.slug || model.brand.id)}
                                                className="ml-2 text-blue-600 hover:text-blue-800"
                                            >
                                                {model.brand.name}
                                            </Link>
                                        </div>

                                        {model.model_number && (
                                            <div>
                                                <span className="text-sm font-medium text-gray-600">Model Number:</span>
                                                <span className="ml-2 text-gray-900">{model.model_number}</span>
                                            </div>
                                        )}
                                        {model.release_year && (
                                            <div>
                                                <span className="text-sm font-medium text-gray-600">Release Year:</span>
                                                <span className="ml-2 text-gray-900">{model.release_year}</span>
                                            </div>
                                        )}
                                        <div>
                                            <span className="text-sm font-medium text-gray-600">Categories:</span>
                                            <span className="ml-2 text-gray-900">
                                                {new Set(model.parts.map(part => part.category.name)).size}
                                            </span>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Actions */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Actions</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        <Button
                                            className="w-full bg-green-600 hover:bg-green-700 text-white"
                                            onClick={handleAddToFavorites}
                                            disabled={isSaved || isAddingToFavorites}
                                        >
                                            <Heart className={`w-4 h-4 mr-2 ${isSaved ? 'fill-current' : ''}`} />
                                            {isSaved ? 'Added to Favorites' : isAddingToFavorites ? 'Adding...' : 'Add to Favorites'}
                                        </Button>

                                        <Link href={route('search.brand', model.brand.slug || model.brand.id) + `?q=${encodeURIComponent(model.name)}`}>
                                            <Button className="w-full">
                                                <Search className="w-4 h-4 mr-2" />
                                                Search {model.name} Parts
                                            </Button>
                                        </Link>

                                        <Link href={route('brands.show', model.brand.slug || model.brand.id)}>
                                            <Button className="w-full" variant="outline">
                                                <Building className="w-4 h-4 mr-2" />
                                                View Brand
                                            </Button>
                                        </Link>
                                        <Link href={route('search.index')}>
                                            <Button className="w-full" variant="outline">
                                                <ArrowLeft className="w-4 h-4 mr-2" />
                                                Back to Search
                                            </Button>
                                        </Link>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
